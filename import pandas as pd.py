import pandas as pd
import pymongo

# Function to load Google Sheet into DataFrame
def load_google_sheet(sheet_url, sheet_name=None):
    csv_url = sheet_url.replace('/edit#gid=', '/export?format=csv&gid=')
    df = pd.read_csv(csv_url)
    return df

# Function to upload DataFrame to MongoDB
def upload_to_mongo(df, collection_name, mongodb_uri, db_name='testdb'):
    client = pymongo.MongoClient(mongodb_uri)
    db = client[db_name]
    collection = db[collection_name]
    collection.insert_many(df.to_dict('records'))
    client.close()

# Main script
def main():
    mongodb_uri = '*************************************************************************************'

    sheets_info = [
        {
            'url': 'https://docs.google.com/spreadsheets/d/12l1sMGHkhRSqXdp_na4EOzblR7VnmFvuzcezyIvtc_Y/edit?gid=48478056#gid=48478056',
            'collection_name': 'sheet1_collection'
        },
        {
            'url': 'https://docs.google.com/spreadsheets/d/1UDDDaXIEEOFY5ldXJcWwlU9BMaUkQW8wg_QwtzC2LeQ/edit?gid=2142087129#gid=2142087129',
            'collection_name': 'sheet2_collection'
        }
    ]

    for sheet in sheets_info:
        df = load_google_sheet(sheet['url'])
        upload_to_mongo(df, sheet['search'], mongodb_uri)
        print(f"Uploaded data from {sheet['url']} to MongoDB collection {sheet['search']}")

if __name__ == "__main__":
    main()
