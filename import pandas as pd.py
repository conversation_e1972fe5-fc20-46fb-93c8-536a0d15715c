import pandas as pd
import requests
from pymongo import MongoClient
from urllib.parse import urlparse, parse_qs
import io

sheet_urls = [
    "https://docs.google.com/spreadsheets/d/1_661lqWvuyIP_nhzpQ_Ncfz48gNqCKTZ-TKLhmvE21A/edit?ouid=108972138408946943568",
    "https://docs.google.com/spreadsheets/d/1_6fBTM9CTgdz8zTag6KIMqEqrUqK_w6QOwXMHwctNqg/edit?ouid=108972138408946943568",
]

client = MongoClient("*************************************************************************************")
db = client["searchleads_sheets"]
collection = db["leads_data"]

for url in sheet_urls:
    try:
        parsed_url = urlparse(url)
        parts = parsed_url.path.split("/")
        sheet_id = parts[3]

        # Check for gid in both query parameters and fragment
        gid = "0"  # default to first sheet

        # Check query parameters first (for URLs like ?gid=123)
        query_params = parse_qs(parsed_url.query)
        if "gid" in query_params:
            gid = query_params["gid"][0]

        # Check fragment for gid (for URLs like #gid=123)
        if parsed_url.fragment:
            fragment_query = parse_qs(parsed_url.fragment)
            if "gid" in fragment_query:
                gid = fragment_query["gid"][0]

        csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv&gid={gid}"
        print(f"Processing sheet: {url}")
        print(f"CSV URL: {csv_url}")

        # Add headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(csv_url, headers=headers)

        if response.status_code == 400:
            print(f"❌ Sheet access denied with standard method. Trying alternative approach...")
            # Try without gid parameter for sheets with ouid
            if "ouid=" in url:
                alt_csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv"
                print(f"   Alternative CSV URL: {alt_csv_url}")
                response = requests.get(alt_csv_url, headers=headers)

            if response.status_code == 400:
                print(f"❌ Still unable to access sheet. Please verify sharing settings.")
                continue
        elif response.status_code == 404:
            print(f"❌ Sheet not found. Please check the URL.")
            continue

        response.raise_for_status()

        if not response.text.strip():
            print(f"❌ Empty response from sheet.")
            continue

        df = pd.read_csv(io.StringIO(response.text))

        records = df.to_dict(orient="records")

        for record in records:
            record["source_sheet"] = url

        if records:
            collection.insert_many(records)
            print(f"✅ Successfully inserted {len(records)} records from sheet")
            print(f"   Data saved to MongoDB collection: {collection.name}")
        else:
            print(f"⚠️  No data found in sheet")

    except Exception as e:
        print(f"❌ Error processing sheet: {str(e)}")
        print(f"   URL: {url}")