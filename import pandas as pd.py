import pandas as pd
import requests
from pymongo import MongoClient
from urllib.parse import urlparse, parse_qs
import io

sheet_urls = [
    "https://docs.google.com/spreadsheets/d/12l1sMGHkhRSqXdp_na4EOzblR7VnmFvuzcezyIvtc_Y/edit?gid=48478056#gid=48478056",
    "https://docs.google.com/spreadsheets/d/1UDDDaXIEEOFY5ldXJcWwlU9BMaUkQW8wg_QwtzC2LeQ/edit?gid=2142087129#gid=2142087129",
]

client = MongoClient("************************************************************************************/")
db = client["searchleads_sheets"]
collection = db["leads_data"]

for url in sheet_urls:
    try:
        parsed_url = urlparse(url)
        parts = parsed_url.path.split("/")
        sheet_id = parts[3]
        query = parse_qs(parsed_url.fragment)
        gid = query.get("gid", ["0"])[0]

        csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv&gid={gid}"

        response = requests.get(csv_url)
        response.raise_for_status()
        df = pd.read_csv(io.StringIO(response.text))

        records = df.to_dict(orient="records")

        for record in records:
            record["source_sheet"] = url

        if records:
            collection.insert_many(records)
            print(f"Inserted {len(records)} records from: {url}")
        else:
            print(f"No data found in: {url}")

    except Exception as e:
        print(f"Error processing {url}: {str(e)}")