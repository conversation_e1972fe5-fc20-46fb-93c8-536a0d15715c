import pandas as pd
import requests
from pymongo import MongoClient
from urllib.parse import urlparse, parse_qs
import io
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from threading import Lock

# Read URLs from CSV file
def load_urls_from_csv(csv_file_path):
    try:
        df = pd.read_csv(csv_file_path)
        urls = df['url'].tolist()
        print(f"✅ Loaded {len(urls)} URLs from {csv_file_path}")
        return urls
    except Exception as e:
        print(f"❌ Error loading URLs from CSV: {str(e)}")
        return []

# Load URLs from the apollo-url.csv file
sheet_urls = load_urls_from_csv("apollo-url.csv")

# MongoDB connection
client = MongoClient("************************************************************************************/")
db = client["searchleads_sheets"]
collection = db["leads_data"]

# Thread-safe counters and failed URLs tracking
stats_lock = Lock()
stats = {
    'processed': 0,
    'successful': 0,
    'failed': 0,
    'total_records': 0
}
failed_urls = []

# Function to process a single sheet URL
def process_sheet(url):
    try:
        parsed_url = urlparse(url)
        parts = parsed_url.path.split("/")
        sheet_id = parts[3]

        # Check for gid in both query parameters and fragment
        gid = "0"  # default to first sheet

        # Check query parameters first (for URLs like ?gid=123)
        query_params = parse_qs(parsed_url.query)
        if "gid" in query_params:
            gid = query_params["gid"][0]

        # Check fragment for gid (for URLs like #gid=123)
        if parsed_url.fragment:
            fragment_query = parse_qs(parsed_url.fragment)
            if "gid" in fragment_query:
                gid = fragment_query["gid"][0]

        csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv&gid={gid}"

        # Add headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(csv_url, headers=headers, timeout=30)

        if response.status_code == 400:
            # Try without gid parameter for sheets with ouid
            if "ouid=" in url:
                alt_csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv"
                response = requests.get(alt_csv_url, headers=headers, timeout=30)

            if response.status_code == 400:
                with stats_lock:
                    stats['failed'] += 1
                    stats['processed'] += 1
                    failed_urls.append(url)
                return f"❌ Access denied: {url}"

        elif response.status_code == 404:
            with stats_lock:
                stats['failed'] += 1
                stats['processed'] += 1
                failed_urls.append(url)
            return f"❌ Not found: {url}"

        response.raise_for_status()

        if not response.text.strip():
            with stats_lock:
                stats['failed'] += 1
                stats['processed'] += 1
                failed_urls.append(url)
            return f"❌ Empty response: {url}"

        # Parse CSV with error handling
        try:
            df = pd.read_csv(io.StringIO(response.text), low_memory=False)
        except Exception as csv_error:
            with stats_lock:
                stats['failed'] += 1
                stats['processed'] += 1
                failed_urls.append(url)
            return f"❌ CSV parse error: {url} - {str(csv_error)}"

        if df.empty:
            with stats_lock:
                stats['failed'] += 1
                stats['processed'] += 1
                failed_urls.append(url)
            return f"⚠️  Empty sheet: {url}"

        records = df.to_dict(orient="records")

        # Add source sheet URL to each record
        for record in records:
            record["source_sheet"] = url

        # Insert into MongoDB
        if records:
            collection.insert_many(records)
            with stats_lock:
                stats['successful'] += 1
                stats['processed'] += 1
                stats['total_records'] += len(records)
            return f"✅ Success: {len(records)} records from {url}"
        else:
            with stats_lock:
                stats['failed'] += 1
                stats['processed'] += 1
                failed_urls.append(url)
            return f"⚠️  No data: {url}"

    except Exception as e:
        with stats_lock:
            stats['failed'] += 1
            stats['processed'] += 1
            failed_urls.append(url)
        return f"❌ Error: {url} - {str(e)}"

# Function to save failed URLs to a file
def save_failed_urls(failed_urls_list, filename="failed_urls.csv"):
    if failed_urls_list:
        failed_df = pd.DataFrame({'url': failed_urls_list})
        failed_df.to_csv(filename, index=False)
        print(f"💾 Saved {len(failed_urls_list)} failed URLs to {filename}")
    else:
        print("✅ No failed URLs to save!")

# Function to load failed URLs from a file
def load_failed_urls(filename="failed_urls.csv"):
    try:
        if pd.io.common.file_exists(filename):
            df = pd.read_csv(filename)
            urls = df['url'].tolist()
            print(f"📁 Loaded {len(urls)} failed URLs from {filename}")
            return urls
        else:
            print(f"📁 No {filename} file found")
            return []
    except Exception as e:
        print(f"❌ Error loading failed URLs: {str(e)}")
        return []

# Function to print progress
def print_progress():
    with stats_lock:
        total_urls = len(sheet_urls)
        processed = stats['processed']
        successful = stats['successful']
        failed = stats['failed']
        total_records = stats['total_records']

        if total_urls > 0:
            progress_pct = (processed / total_urls) * 100
            print(f"\n📊 Progress: {processed}/{total_urls} ({progress_pct:.1f}%) | ✅ {successful} success | ❌ {failed} failed | 📝 {total_records} total records")

# Main execution with multithreading
def main(retry_failed=False):
    global sheet_urls, failed_urls, stats

    if retry_failed:
        # Load failed URLs for retry
        retry_urls = load_failed_urls()
        if not retry_urls:
            print("❌ No failed URLs to retry. Exiting.")
            return
        sheet_urls = retry_urls
        # Reset stats for retry
        stats = {'processed': 0, 'successful': 0, 'failed': 0, 'total_records': 0}
        failed_urls = []
        print(f"🔄 RETRY MODE: Processing {len(sheet_urls)} failed URLs...")
    else:
        if not sheet_urls:
            print("❌ No URLs to process. Exiting.")
            return
        print(f"🚀 Starting to process {len(sheet_urls)} Google Sheets with multithreading...")

    print(f"📊 Using {min(10, len(sheet_urls))} threads")

    start_time = time.time()

    # Use ThreadPoolExecutor for multithreading
    with ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all tasks
        future_to_url = {executor.submit(process_sheet, url): url for url in sheet_urls}

        # Process completed tasks
        for future in as_completed(future_to_url):
            result = future.result()
            print(result)

            # Print progress every 50 completed tasks
            if stats['processed'] % 50 == 0:
                print_progress()

    # Final summary
    end_time = time.time()
    duration = end_time - start_time

    print(f"\n🎉 Processing Complete!")
    print(f"⏱️  Total time: {duration:.2f} seconds")
    print(f"📊 Final stats:")
    print(f"   • Total URLs: {len(sheet_urls)}")
    print(f"   • Successful: {stats['successful']}")
    print(f"   • Failed: {stats['failed']}")
    print(f"   • Total records inserted: {stats['total_records']}")
    print(f"   • Average time per sheet: {duration/len(sheet_urls):.2f} seconds")

    # Save failed URLs for potential retry
    if failed_urls:
        save_failed_urls(failed_urls)
        print(f"\n🔄 To retry failed URLs, run: python \"{__file__}\" --retry")
    else:
        print(f"\n✅ All URLs processed successfully!")

# Function to retry failed URLs
def retry_failed():
    main(retry_failed=True)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--retry":
        retry_failed()
    else:
        main()