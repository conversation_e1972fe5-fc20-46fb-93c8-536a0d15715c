import pandas as pd
import requests
from pymongo import MongoClient
from urllib.parse import urlparse, parse_qs
import io
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import random

# MongoDB connection
client = MongoClient("************************************************************************************/")
db = client["searchleads_sheets"]
collection = db["leads_data"]

# Thread-safe counters
stats_lock = Lock()
stats = {'processed': 0, 'successful': 0, 'failed': 0, 'total_records': 0}
failed_urls = []

def get_already_processed_urls():
    """Get list of URLs that have already been successfully processed"""
    try:
        # Get distinct source_sheet URLs from MongoDB
        processed_urls = collection.distinct("source_sheet")
        print(f"📊 Found {len(processed_urls)} already processed URLs in MongoDB")
        return set(processed_urls)
    except Exception as e:
        print(f"❌ Error checking processed URLs: {str(e)}")
        return set()

def get_failed_urls():
    """Get list of URLs that need to be processed (not in MongoDB)"""
    try:
        # Load all URLs from CSV
        df = pd.read_csv("apollo-url.csv")
        all_urls = set(df['url'].tolist())
        print(f"📁 Loaded {len(all_urls)} total URLs from apollo-url.csv")
        
        # Get already processed URLs
        processed_urls = get_already_processed_urls()
        
        # Find URLs that haven't been processed
        failed_urls = all_urls - processed_urls
        failed_urls_list = list(failed_urls)
        
        print(f"🔍 Found {len(failed_urls_list)} URLs that need to be processed")
        print(f"✅ Already processed: {len(processed_urls)}")
        print(f"❌ Still need processing: {len(failed_urls_list)}")
        
        return failed_urls_list
        
    except Exception as e:
        print(f"❌ Error identifying failed URLs: {str(e)}")
        return []

def process_sheet_with_retry(url, max_retries=3):
    """Process a single sheet with retry logic and exponential backoff"""
    
    for attempt in range(max_retries):
        try:
            # Add random delay to avoid overwhelming the server
            time.sleep(random.uniform(2, 5))
            
            parsed_url = urlparse(url)
            parts = parsed_url.path.split("/")
            sheet_id = parts[3]

            # Check for gid in both query parameters and fragment
            gid = "0"  # default to first sheet

            # Check query parameters first (for URLs like ?gid=123)
            query_params = parse_qs(parsed_url.query)
            if "gid" in query_params:
                gid = query_params["gid"][0]

            # Check fragment for gid (for URLs like #gid=123)
            if parsed_url.fragment:
                fragment_query = parse_qs(parsed_url.fragment)
                if "gid" in fragment_query:
                    gid = fragment_query["gid"][0]

            # Add headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # Try standard CSV URL first
            csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv&gid={gid}"
            response = requests.get(csv_url, headers=headers, timeout=90)

            if response.status_code == 400:
                # Try without gid parameter for sheets with ouid
                if "ouid=" in url:
                    alt_csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/export?format=csv"
                    response = requests.get(alt_csv_url, headers=headers, timeout=90)

            if response.status_code in [200]:
                response.raise_for_status()

                if response.text.strip():
                    # Parse CSV with error handling
                    try:
                        df = pd.read_csv(io.StringIO(response.text), low_memory=False)
                        
                        if not df.empty:
                            records = df.to_dict(orient="records")

                            # Add source sheet URL to each record
                            for record in records:
                                record["source_sheet"] = url

                            # Insert into MongoDB
                            if records:
                                collection.insert_many(records)
                                with stats_lock:
                                    stats['successful'] += 1
                                    stats['processed'] += 1
                                    stats['total_records'] += len(records)
                                return f"✅ Success (attempt {attempt + 1}): {len(records)} records from {url}"
                        else:
                            with stats_lock:
                                stats['successful'] += 1  # Empty sheet is still "successful"
                                stats['processed'] += 1
                            return f"⚠️  Empty sheet (attempt {attempt + 1}): {url}"
                    
                    except Exception as csv_error:
                        if attempt == max_retries - 1:  # Last attempt
                            with stats_lock:
                                stats['failed'] += 1
                                stats['processed'] += 1
                                failed_urls.append(url)
                            return f"❌ CSV parse error (final attempt): {url} - {str(csv_error)}"
                        else:
                            print(f"⚠️  CSV parse error (attempt {attempt + 1}): {url} - retrying in {2 ** attempt} seconds...")
                            time.sleep(2 ** attempt)  # Exponential backoff
                            continue

            # If we get here, the request failed
            if attempt == max_retries - 1:  # Last attempt
                with stats_lock:
                    stats['failed'] += 1
                    stats['processed'] += 1
                    failed_urls.append(url)
                return f"❌ Failed (final attempt): {url} - Status: {response.status_code}"
            else:
                print(f"⚠️  Failed (attempt {attempt + 1}): {url} - Status: {response.status_code} - retrying in {2 ** attempt} seconds...")
                time.sleep(2 ** attempt)  # Exponential backoff

        except Exception as e:
            if attempt == max_retries - 1:  # Last attempt
                with stats_lock:
                    stats['failed'] += 1
                    stats['processed'] += 1
                    failed_urls.append(url)
                return f"❌ Error (final attempt): {url} - {str(e)}"
            else:
                print(f"⚠️  Error (attempt {attempt + 1}): {url} - {str(e)} - retrying in {2 ** attempt} seconds...")
                time.sleep(2 ** attempt)  # Exponential backoff

def main():
    """Process only the failed URLs"""
    
    print("🔍 Identifying URLs that need to be processed...")
    urls_to_process = get_failed_urls()
    
    if not urls_to_process:
        print("✅ All URLs have already been processed successfully!")
        return
    
    print(f"\n🔄 Starting to process {len(urls_to_process)} failed URLs...")
    print(f"📊 Using 3 threads with enhanced retry logic")
    print(f"🔄 Each URL will be retried up to 3 times with exponential backoff")
    print(f"⏰ Using 90-second timeout per request")
    
    start_time = time.time()
    
    # Use reduced number of threads to avoid overwhelming the servers
    with ThreadPoolExecutor(max_workers=3) as executor:
        # Submit all tasks
        future_to_url = {executor.submit(process_sheet_with_retry, url): url for url in urls_to_process}
        
        # Process completed tasks
        for future in as_completed(future_to_url):
            result = future.result()
            print(result)
            
            # Print progress every 10 completed tasks
            if stats['processed'] % 10 == 0:
                with stats_lock:
                    total_urls = len(urls_to_process)
                    processed = stats['processed']
                    successful = stats['successful']
                    failed = stats['failed']
                    total_records = stats['total_records']
                    
                    if total_urls > 0:
                        progress_pct = (processed / total_urls) * 100
                        print(f"\n📊 Progress: {processed}/{total_urls} ({progress_pct:.1f}%) | ✅ {successful} success | ❌ {failed} failed | 📝 {total_records} total records\n")
    
    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n🎉 Failed URLs Processing Complete!")
    print(f"⏱️  Total time: {duration:.2f} seconds")
    print(f"📊 Final stats:")
    print(f"   • URLs processed: {len(urls_to_process)}")
    print(f"   • Successful: {stats['successful']}")
    print(f"   • Still failed: {stats['failed']}")
    print(f"   • Total records inserted: {stats['total_records']}")
    if len(urls_to_process) > 0:
        print(f"   • Average time per sheet: {duration/len(urls_to_process):.2f} seconds")
    
    # Save any remaining failed URLs
    if failed_urls:
        failed_df = pd.DataFrame({'url': failed_urls})
        failed_df.to_csv("still_failed_urls.csv", index=False)
        print(f"\n💾 Saved {len(failed_urls)} still-failed URLs to still_failed_urls.csv")
    else:
        print(f"\n✅ All failed URLs processed successfully!")

if __name__ == "__main__":
    main()
